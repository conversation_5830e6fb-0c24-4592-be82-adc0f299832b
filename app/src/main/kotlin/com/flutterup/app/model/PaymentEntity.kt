package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class StoreSubscriptionEntity(
    @Json(name = "rights") val rights: List<SubscriptionBenefitEntity>? = null,
    @Json(name = "shops") val shops: List<SubscriptionItem>? = null,
    @<PERSON><PERSON>(name = "page_desc1") val pageDesc1: String? = null,
    @<PERSON><PERSON>(name = "page_desc2") val pageDesc2: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class SubscriptionBenefitEntity(
    @J<PERSON>(name = "icon") val icon: String? = null,
    @<PERSON><PERSON>(name = "icon_unselect") val iconUnselect: String? = null,
    @Json(name = "title") val title: String? = null,
    @<PERSON><PERSON>(name = "desc") val desc: String? = null,
    @<PERSON><PERSON>(name = "background") val background: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class SubscriptionItem(
    @Json(name = "time") val time: String? = null,
    @Json(name = "unit") val unit: String? = null,
    @J<PERSON>(name = "save") val save: String? = null,
    @Json(name = "price") val price: String? = null,
    @Json(name = "average") val average: String? = null,
    @Json(name = "hot") val hot: Int? = null,
    @Json(name = "prod_id") val prodId: String? = null,
    @Json(name = "product_type") val prodType: String? = null,
    @Json(name = "subscribing") val subscribing: Int? = null,
    @Json(name = "name") val name: String? = null,
    @Json(name = "price_value") val priceValue: Int? = null,
    @Json(name = "price_fvalue") val priceFValue: Float? = null,
    @Json(name = "currency") val currency: String? = null,
    @Json(name = "priceId") val priceId: String? = null
)
