package com.flutterup.app.screen.payment.state

import com.flutterup.app.model.SubscriptionBenefitEntity
import com.flutterup.app.model.SubscriptionItem

data class SubscriptUiState(

    val benefits: List<SubscriptionBenefitEntity> = List(8) {
        SubscriptionBenefitEntity(
            icon = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            iconUnselect = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            title = "test$it, aewae",
            desc = "test$it, tetettetetetetteasddklamkmkzmkcmxko1ommkmczmx1mklmadkmmmdm",
            background = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
        )
    },

    val items: List<SubscriptionItem> = emptyList()
)