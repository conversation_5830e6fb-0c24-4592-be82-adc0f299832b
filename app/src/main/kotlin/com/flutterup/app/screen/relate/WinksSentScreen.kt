@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.relate

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.WinkItemEntity
import com.flutterup.app.model.WinkType
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.profile.ProfileOtherRoute
import com.flutterup.app.screen.relate.component.WinksItem
import com.flutterup.app.screen.relate.state.WinksSentState
import com.flutterup.app.screen.relate.vm.WinksSentViewModel
import com.flutterup.base.compose.refresh.RefreshLoadMoreBox
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState

@Composable
fun WinksSentScreen() {
    val navController = LocalNavController.current
    val viewModel = hiltViewModel<WinksSentViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    WinksSentContent(
        uiState = uiState,
        onRefresh = { viewModel.refresh() },
        onLoadMore = { viewModel.loadMore() },
        onAvatarClick = {
            navController.navigate(
                ProfileOtherRoute(
                    userId = it.userId.orEmpty(),
                    from = AppFrom.WinkReceived
                )
            )
        },
        onBottomClick = {
            //TODO 跳转 ping chat
        }
    )
}

@Composable
private fun WinksSentContent(
    uiState: WinksSentState,
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
    onAvatarClick: (WinkItemEntity) -> Unit = {},
    onBottomClick: (WinkItemEntity) -> Unit = {},
) {
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    RefreshLoadMoreBox(
        isRefreshing = uiState.isRefreshing,
        isLoadingMore = uiState.isLoadingMore,
        hasNoMoreData = uiState.hasNoMoreData,
        onRefresh = onRefresh,
        onLoadMore = onLoadMore,
        pullLoadMoreState = pullToLoadMoreState,
        verticalArrangement = Arrangement.spacedBy(10.dp),
        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 20.dp),
        loadMoreIndicator = {
            AppLoadMoreIndicator(
                isLoadingMore = uiState.isLoadingMore,
                hasNoMoreData = uiState.hasNoMoreData,
                state = pullToLoadMoreState
            )
        }
    ) {
        items(uiState.winks, key = { it.id }) {
            WinksItem(
                winkType = WinkType.Sent,
                item = it,
                blurEnabled = false,
                onAvatarClick = { onAvatarClick(it) },
                onBottomClick = { onBottomClick(it) }
            )
        }
    }
}


@Preview
@Composable
private fun WinksSentScreenPreview() {
    AppTheme {
        WinksSentContent(WinksSentState())
    }
}