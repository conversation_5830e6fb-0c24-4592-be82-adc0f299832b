package com.flutterup.app.screen.payment

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.toRoute
import com.flutterup.app.model.AppPaymentFrom
import kotlinx.serialization.Serializable

@Serializable data class PaymentSubscriptionRoute(
    val eventFrom: AppPaymentFrom,
    val expireTime: Long? = null,
)

@Serializable data object PaymentPacksRoute


fun NavGraphBuilder.paymentGraph() {
    composable<PaymentSubscriptionRoute> {
        val data = it.toRoute<PaymentSubscriptionRoute>()
        SubscriptionScreen(
            eventFrom = data.eventFrom,
            expireTime = data.expireTime,
        )
    }

    dialog<PaymentPacksRoute> {
        PaymentPacksScreen()
    }
}