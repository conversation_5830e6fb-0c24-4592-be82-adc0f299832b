package com.flutterup.app.screen.payment.vm

import com.flutterup.app.screen.payment.state.SubscriptUiState
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class SubscriptionViewModel @Inject constructor(

) : BaseViewModel() {

    private val _uiState = MutableStateFlow(SubscriptUiState())

    val uiState = _uiState.asStateFlow()
}