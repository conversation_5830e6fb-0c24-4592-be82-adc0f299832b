@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.payment

import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.coordinator.CoordinatorLayout
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.SubscriptionBenefitEntity
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.payment.component.SubscriptionBenefits
import com.flutterup.app.screen.payment.component.SubscriptionStores
import com.flutterup.app.screen.payment.state.SubscriptUiState
import com.flutterup.app.screen.payment.vm.SubscriptionViewModel

@Composable
fun SubscriptionScreen(
    eventFrom: AppPaymentFrom,
    expireTime: Long?
) {
    val navController = LocalNavController.current
    val viewModel = hiltViewModel<SubscriptionViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()


    SubscriptionContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
    )
}


@Composable
private fun SubscriptionContent(
    uiState: SubscriptUiState,
    onBackClick: () -> Unit = {},
) {
    val crossHeight: Dp = 32.dp
    val scrollState = rememberScrollState()


    AppScaffold(
        title = {},
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        CoordinatorLayout(
            nestedScrollableState = { scrollState },
            collapsableContent = {
                SubscriptionBenefits(
                    modifier = Modifier,
                    uiState = uiState,
                    paddingValues = paddingValues,
                    crossHeight = crossHeight,
                )
            }
        ) {
            SubscriptionStores(
                modifier = Modifier,
                uiState = uiState,
                crossHeight = crossHeight,
                scrollState = scrollState,
            )
        }

//        Column {
//            SubscriptionBenefits(
//                uiState = uiState,
//                paddingValues = paddingValues,
//                crossHeight = crossHeight,
//                modifier = Modifier
//            )
//
//            Box(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .wrapContentHeight()
//                    .offset(y = -crossHeight)
//            ) {
//                Image(
//                    painter = painterResource(R.mipmap.ic_subscription_bottom_background),
//                    contentDescription = null,
//                    contentScale = ContentScale.FillBounds,
//                    modifier = Modifier.fillMaxSize()
//                )
//            }
//        }
    }
}

@Preview
@Composable
private fun SubscriptionScreenPreview() {
    AppTheme {
        SubscriptionContent(
            uiState = SubscriptUiState(
                benefits = List(7) {
                    SubscriptionBenefitEntity(
                        icon = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        iconUnselect = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        title = "test$it, aewae",
                        desc = "test$it, tetettetetetetteasddklamkmkzmkcmxko1ommkmczmx1mklmadkmmmdm",
                        background = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
                    )
                }
            )
        )
    }
}