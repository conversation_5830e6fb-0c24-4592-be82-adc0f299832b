@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.relate

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LineSecondary
import com.flutterup.app.design.theme.TextBlack151
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextPurple571
import com.flutterup.app.model.VisitorItemEntity
import com.flutterup.app.screen.relate.component.starring.StarRing
import com.flutterup.app.screen.relate.state.VisitorState
import com.flutterup.app.screen.relate.vm.VisitorViewModel
import com.flutterup.base.compose.refresh.RefreshLoadMoreBox
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState
import dev.chrisbanes.haze.hazeEffect


@Composable
fun VisitorScreen() {
    val viewModel: VisitorViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(uiState.hasUnreadVisitor) {
        if (uiState.hasUnreadVisitor) {
            viewModel.markAllAsRead()
        }
    }

    if (uiState.isVip) {
        VisitorListContent(
            uiState = uiState,
            onRefresh = { viewModel.refresh() },
            onLoadMore = { viewModel.loadMore() },
        )
    } else {
        RandomVisitorContent(
            uiState = uiState,
            onVipClick = {
                //TODO("跳转会员页")
            }
        )
    }
}

/**
 * 非会员状态下
 */
@Composable
private fun RandomVisitorContent(
    uiState: VisitorState,
    onVipClick: () -> Unit = {},
) {
    Column(Modifier.fillMaxSize()) {
        if (uiState.visitorLatestAvatar != null) {
            Row(
                modifier = Modifier
                    .padding(start = 73.dp, end = 73.dp, top = 20.dp)
                    .fillMaxWidth()
                    .height(28.dp)
                    .clip(RoundedCornerShape(20.dp))
                    .background(TextPurple571.copy(0.1f)),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Spacer(Modifier.width(20.dp))

                AsyncImage(
                    model = uiState.visitorLatestAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(18.dp)
                        .hazeEffect()
                )

                Spacer(Modifier.width(20.dp))

                Text(
                    text = stringResource(R.string.new_visitors, uiState.visitorNewCount),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W300,
                        color = TextPurple571,
                        textAlign = TextAlign.Center,
                    ),
                )
            }
        }



        Box(
            modifier = Modifier.weight(1f)
        ) {
            StarRing(
                ringAvatars = uiState.random,
                modifier = Modifier.align(Alignment.Center)
            ) {
                AsyncImage(
                    model = uiState.userAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(96.dp)
                )
            }
        }

        Column(
            modifier = Modifier
                .padding(bottom = 55.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_visitor_vip),
                contentDescription = null,
                modifier = Modifier
                    .padding(horizontal = 48.dp)
                    .fillMaxWidth()
                    .height(48.dp)
                    .noRippleClickable(
                        onClick = onVipClick,
                        role = Role.Button
                    )
            )

            Spacer(Modifier.height(13.dp))

            Text(
                text = stringResource(R.string.visitor_desc),
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    color = TextPurple571,
                )
            )
        }
    }
}

/**
 * 会员状态下
 */
@Composable
private fun VisitorListContent(
    uiState: VisitorState,
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
) {
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    RefreshLoadMoreBox(
        isRefreshing = uiState.isRefreshing,
        isLoadingMore = uiState.isLoadingMore,
        hasNoMoreData = uiState.hasNoMoreData,
        onRefresh = onRefresh,
        onLoadMore = onLoadMore,
        pullLoadMoreState = pullToLoadMoreState,
        verticalArrangement = Arrangement.spacedBy(14.dp),
        contentPadding = PaddingValues(horizontal = 20.dp),
        loadMoreIndicator = {
            AppLoadMoreIndicator(
                isLoadingMore = uiState.isLoadingMore,
                hasNoMoreData = uiState.hasNoMoreData,
                state = pullToLoadMoreState
            )
        }
    ) {
        items(uiState.visitors, key = { it.id }) {
            VisitorItem(
                visitor = it,
                modifier = Modifier.height(65.dp)
            )
        }
    }
}

@Composable
private fun VisitorItem(
    visitor: VisitorItemEntity,
    modifier: Modifier = Modifier,
) {
    Box(modifier) {
        Row(Modifier.align(Alignment.TopStart)) {
            AppAvatar(online = visitor.online == 1) {
                AsyncImage(
                    model = visitor.headimg,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(50.dp)
                )
            }

            Spacer(Modifier.width(12.dp))

            Column(
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = visitor.nickname.orEmpty(),
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.W900,
                            color = TextBlack151,
                        )
                    )

                    if (visitor.nearby == 1) {
                        Spacer(Modifier.width(5.dp))

                        Icon(
                            painter = painterResource(R.drawable.ic_visitor_nearby),
                            contentDescription = null,
                            tint = Color.Unspecified,
                            modifier = Modifier.size(10.dp)
                        )
                    }

                    if (visitor.read == 0) {
                        Spacer(Modifier.width(5.dp))

                        Icon(
                            painter = painterResource(R.drawable.ic_visitor_new),
                            contentDescription = null,
                            tint = Color.Unspecified,
                            modifier = Modifier.size(width = 25.dp, height = 10.dp)
                        )
                    }
                }

                Spacer(Modifier.height(8.dp))

                Text(
                    text = stringResource(R.string.visitor_visit_count, visitor.viewCount ?: 0),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack666,
                    )
                )
            }
        }

        HorizontalDivider(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .fillMaxWidth(),
            thickness = 1.dp,
            color = LineSecondary
        )
    }
}

@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
@Composable
private fun RandomVisitorContentPreview() {
    AppTheme {
        RandomVisitorContent(
            uiState = VisitorState.EMPTY
        )
    }
}

@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
@Composable
private fun VisitorListContentPreview() {
    AppTheme {
        VisitorListContent(
            uiState = VisitorState.EMPTY.copy(
                visitors = listOf(
                    VisitorItemEntity(
                        id = 1,
                        headimg = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        nickname = "lalala",
                        online = 1,
                        nearby = 1,
                        read = 0,
                        viewCount = 100,
                    ),

                    VisitorItemEntity(
                        id = 2,
                        headimg = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        nickname = "lalala",
                        online = 1,
                        nearby = 1,
                        read = 0,
                        viewCount = 100,
                    ),

                    VisitorItemEntity(
                        id = 3,
                        headimg = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        nickname = "lalala",
                        online = 1,
                        nearby = 1,
                        read = 0,
                        viewCount = 100,
                    )
                )
            )
        )
    }
}