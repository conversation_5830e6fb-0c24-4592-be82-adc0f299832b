package com.flutterup.app.screen.payment.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.CompositingStrategy
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.clipRect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.payment.state.SubscriptUiState


@Composable
fun SubscriptionStores(
    modifier: Modifier = Modifier,
    uiState: SubscriptUiState,
    crossHeight: Dp,
    scrollState: ScrollState,
) {
    TopCapBorder(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .offset(y = -crossHeight)
            .verticalScroll(scrollState)
    ) {
        Column(modifier = Modifier.height(900.dp)) {

        }
    }
}

@Composable
private fun TopCapBorder(
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit = {}
) {

}


@Composable
private fun SubscriptionItem() {

}