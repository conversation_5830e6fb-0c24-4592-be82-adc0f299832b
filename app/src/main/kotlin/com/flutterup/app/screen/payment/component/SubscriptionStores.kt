package com.flutterup.app.screen.payment.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.flutterup.app.screen.payment.state.SubscriptUiState


@Composable
fun SubscriptionStores(
    modifier: Modifier = Modifier,
    uiState: SubscriptUiState,
    crossHeight: Dp,
    scrollState: ScrollState,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .offset(y = -crossHeight)
            .topCurvedBackground()
            .verticalScroll(scrollState)
    ) {
        Column(modifier = Modifier.height(900.dp)) {

        }
    }
}


fun Modifier.topCurvedBackground(
    backgroundColor: Color = Color.White,
    curveDepth: Dp = 16.dp,
    cornerRadius: Dp = 16.dp
) = this.drawBehind {
    val width = size.width
    val height = size.height
    val curveDepthPx = curveDepth.toPx()
    val cornerRadiusPx = cornerRadius.toPx()

    val path = Path().apply {
        moveTo(0f, cornerRadiusPx)

        // 左上圆角
        arcTo(
            rect = Rect(0f, 0f, cornerRadiusPx * 2, cornerRadiusPx * 2),
            startAngleDegrees = 180f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )

        // 到凹陷起始点
        lineTo(width * 0.35f, 0f)

        // 凹陷曲线
        quadraticTo(
            width * 0.5f, curveDepthPx,
            width * 0.65f, 0f
        )

        // 到右上角
        lineTo(width - cornerRadiusPx, 0f)

        // 右上圆角
        arcTo(
            rect = Rect(
                width - cornerRadiusPx * 2, 0f,
                width, cornerRadiusPx * 2
            ),
            startAngleDegrees = 270f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )

        lineTo(width, height)
        lineTo(0f, height)
        close()
    }

    drawPath(path, backgroundColor)
}

@Composable
private fun SubscriptionItem() {

}