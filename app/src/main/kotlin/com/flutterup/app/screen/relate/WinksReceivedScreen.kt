@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.relate

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.WinkItemEntity
import com.flutterup.app.model.WinkType
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.profile.ProfileOtherRoute
import com.flutterup.app.screen.relate.component.WinksItem
import com.flutterup.app.screen.relate.state.WinksReceivedState
import com.flutterup.app.screen.relate.vm.WinksReceivedViewModel
import com.flutterup.base.compose.refresh.RefreshLoadMoreBox
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState
import com.flutterup.base.utils.Timber


@Composable
fun WinksReceivedScreen() {
    val navController = LocalNavController.current
    val viewModel: WinksReceivedViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    //再次进入，有未读消息时，标记为已读
    LaunchedEffect(uiState.hasUnreadWink) {
        if (uiState.hasUnreadWink) {
            viewModel.markAllAsRead()
        }
    }

    WinksReceivedContent(
        uiState = uiState,
        onRefresh = { viewModel.markAsReadThenRefresh() },
        onLoadMore = { viewModel.loadMore() },
        onAvatarClick = {
            if (!uiState.isVip) {
                //TODO 跳转会员页
                Timber.showToast("跳转会员页")
                return@WinksReceivedContent
            }
            navController.navigate(
                ProfileOtherRoute(
                    userId = it.userId.orEmpty(),
                    from = AppFrom.WinkReceived
                )
            )
        },
        onDislikeClick = { viewModel.dislike(it) },
        onLikeClick = { viewModel.like(it) },
        onBottomClick = {
            //TODO 跳转 ping chat
        }
    )
}

@Composable
private fun WinksReceivedContent(
    uiState: WinksReceivedState,
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
    onAvatarClick: (WinkItemEntity) -> Unit = {},
    onDislikeClick: (WinkItemEntity) -> Unit = {},
    onLikeClick: (WinkItemEntity) -> Unit = {},
    onBottomClick: (WinkItemEntity) -> Unit = {},
) {
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    RefreshLoadMoreBox(
        isRefreshing = uiState.isRefreshing,
        isLoadingMore = uiState.isLoadingMore,
        hasNoMoreData = uiState.hasNoMoreData,
        onRefresh = onRefresh,
        onLoadMore = onLoadMore,
        pullLoadMoreState = pullToLoadMoreState,
        verticalArrangement = Arrangement.spacedBy(10.dp),
        contentPadding = PaddingValues(horizontal = 12.dp),
        loadMoreIndicator = {
            AppLoadMoreIndicator(
                isLoadingMore = uiState.isLoadingMore,
                hasNoMoreData = uiState.hasNoMoreData,
                state = pullToLoadMoreState
            )
        }
    ) {
        items(uiState.winks, key = { it.id }) {
            WinksItem(
                winkType = WinkType.Received,
                item = it,
                blurEnabled = !uiState.isVip,
                onAvatarClick = { onAvatarClick(it) },
                onDislikeClick = { onDislikeClick(it) },
                onLikeClick = { onLikeClick(it) },
                onBottomClick = { onBottomClick(it) }
            )
        }
    }
}

@Preview
@Composable
private fun WinksReceivedScreenPreview() {
    AppTheme {
        AppBackground {
            WinksReceivedContent(
                uiState = WinksReceivedState()
            )
        }
    }
}