package com.flutterup.app.screen.relate.state

import com.flutterup.app.model.VisitorItemEntity
import com.flutterup.app.utils.randomBoolean

/**
 * @param visitorNewCount 新visitor数量
 * @param userAvatar 用户头像
 * @param visitorLatestAvatar 最后访客头像
 * @param random 随机访客
 */
data class VisitorState(
    val visitorNewCount: Int,
    val userAvatar: String? = null,
    val visitorLatestAvatar: String? = null,
    val random: List<VisitorRandom> = emptyList(),
    val isVip: Boolean = false,


    val isRefreshing: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasNoMoreData: Boolean = false,
    val visitors: List<VisitorItemEntity> = emptyList(),
) {

    val hasUnreadVisitor = visitors.isNotEmpty() && visitors.any { it.read == 0 }

    companion object {
        val EMPTY = VisitorState(0, random = emptyList())
    }
}

data class VisitorRandom(
    val avatar: String?,
    val isNearby: Boolean = false,
    val isOnline: Boolean = false,
)